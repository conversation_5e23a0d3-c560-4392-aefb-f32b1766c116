@page "/devices"
@using AttendanceSystem.Core.Models
@using AttendanceSystem.Infrastructure.Services
@inject AttendanceDbContext DbContext
@inject MqttService MqttService
@implements IDisposable

<PageTitle>Device Status</PageTitle>

<h3>Attendance Machines Status</h3>

<div class="row mb-3">
    <div class="col">
        <button class="btn btn-primary" @onclick="RefreshDevices">Refresh</button>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Name</th>
                <th>Serial Number</th>
                <th>IP Address</th>
                <th>Location</th>
                <th>Status</th>
                <th>Last Seen</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var machine in machines)
            {
                <tr>
                    <td>@machine.Name</td>
                    <td>@machine.SerialNumber</td>
                    <td>@machine.IpAddress</td>
                    <td>@machine.Location</td>
                    <td>
                        <span class="badge @(machine.IsOnline ? "bg-success" : "bg-danger")">
                            @(machine.IsOnline ? "Online" : "Offline")
                        </span>
                    </td>
                    <td>@machine.LastSeen.ToString("yyyy-MM-dd HH:mm:ss")</td>
                </tr>
            }
        </tbody>
    </table>
</div>

@code {
    private List<AttendanceMachine> machines = new();

    protected override async Task OnInitializedAsync()
    {
        MqttService.DeviceStatusChanged += OnDeviceStatusChanged;
        await RefreshDevices();
    }

    private async Task RefreshDevices()
    {
        machines = await DbContext.AttendanceMachines.ToListAsync();
        StateHasChanged();
    }

    private async void OnDeviceStatusChanged(string deviceId, bool isOnline)
    {
        var machine = machines.FirstOrDefault(m => m.SerialNumber == deviceId);
        if (machine != null)
        {
            machine.IsOnline = isOnline;
            machine.LastSeen = DateTime.UtcNow;
            
            DbContext.Update(machine);
            await DbContext.SaveChangesAsync();
            
            await InvokeAsync(StateHasChanged);
        }
    }

    public void Dispose()
    {
        MqttService.DeviceStatusChanged -= OnDeviceStatusChanged;
    }
}