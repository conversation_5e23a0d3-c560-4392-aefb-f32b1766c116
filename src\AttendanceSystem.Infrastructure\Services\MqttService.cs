using MQTTnet;
using MQTTnet.Client;
using Microsoft.Extensions.Logging;
using AttendanceSystem.Core.Models;

namespace AttendanceSystem.Infrastructure.Services;

public class MqttService : IDisposable
{
    private readonly IMqttClient _mqttClient;
    private readonly ILogger<MqttService> _logger;
    private readonly string _brokerHost;
    private readonly int _brokerPort;

    public event Action<string, bool>? DeviceStatusChanged;

    public MqttService(ILogger<MqttService> logger, string brokerHost = "localhost", int brokerPort = 1883)
    {
        _logger = logger;
        _brokerHost = brokerHost;
        _brokerPort = brokerPort;
        
        var factory = new MqttFactory();
        _mqttClient = factory.CreateMqttClient();
        
        _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceived;
        _mqttClient.DisconnectedAsync += OnDisconnected;
    }

    public async Task ConnectAsync()
    {
        var options = new MqttClientOptionsBuilder()
            .WithTcpServer(_brokerHost, _brokerPort)
            .WithClientId($"AttendanceSystem_{Guid.NewGuid()}")
            .Build();

        await _mqttClient.ConnectAsync(options);
        
        // Subscribe to device status topics
        await _mqttClient.SubscribeAsync("attendance/+/status");
        await _mqttClient.SubscribeAsync("attendance/+/heartbeat");
    }

    private Task OnMessageReceived(MqttApplicationMessageReceivedEventArgs e)
    {
        var topic = e.ApplicationMessage.Topic;
        var payload = System.Text.Encoding.UTF8.GetString(e.ApplicationMessage.Payload);
        
        // Parse device ID from topic
        var topicParts = topic.Split('/');
        if (topicParts.Length >= 3)
        {
            var deviceId = topicParts[1];
            var messageType = topicParts[2];
            
            if (messageType == "status" || messageType == "heartbeat")
            {
                var isOnline = payload.Equals("online", StringComparison.OrdinalIgnoreCase);
                DeviceStatusChanged?.Invoke(deviceId, isOnline);
            }
        }
        
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        _mqttClient?.Dispose();
    }
}